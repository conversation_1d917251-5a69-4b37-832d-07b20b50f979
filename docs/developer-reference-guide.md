# BV Quote App - Developer Reference Guide

## Table of Contents
- [Laravel 12 Resources](#laravel-12-resources)
- [Bootstrap 5 Resources](#bootstrap-5-resources)
- [Chatwoot Resources](#chatwoot-resources)
- [Database & Versioning](#database--versioning)
- [Security Implementation](#security-implementation)
- [AWS Deployment](#aws-deployment)
- [Development Tools](#development-tools)
- [Testing Resources](#testing-resources)
- [Project-Specific Examples](#project-specific-examples)

## Laravel 12 Resources

### Official Documentation
- **Laravel 12 Documentation**: https://laravel.com/docs/12.x
- **Installation Guide**: https://laravel.com/docs/12.x/installation
- **Configuration**: https://laravel.com/docs/12.x/configuration
- **Routing**: https://laravel.com/docs/12.x/routing
- **Blade Templates**: https://laravel.com/docs/12.x/blade
- **Database Migrations**: https://laravel.com/docs/12.x/migrations
- **Eloquent ORM**: https://laravel.com/docs/12.x/eloquent

### Project-Specific Laravel Features
- **URL Generation**: https://laravel.com/docs/12.x/urls
- **Validation**: https://laravel.com/docs/12.x/validation
- **Caching**: https://laravel.com/docs/12.x/cache
- **Session Management**: https://laravel.com/docs/12.x/session
- **CSRF Protection**: https://laravel.com/docs/12.x/csrf
- **Environment Configuration**: https://laravel.com/docs/12.x/configuration#environment-configuration
- **Database Connections**: https://laravel.com/docs/12.x/database#configuration

### Laravel Telescope (Development Debugging)
- **Laravel Telescope Documentation**: https://laravel.com/docs/12.x/telescope
- **Installation**: https://laravel.com/docs/12.x/telescope#installation
- **Configuration**: https://laravel.com/docs/12.x/telescope#configuration
- **Telescope GitHub**: https://github.com/laravel/telescope

### Laravel Security
- **Authentication**: https://laravel.com/docs/12.x/authentication
- **Authorization**: https://laravel.com/docs/12.x/authorization
- **Encryption**: https://laravel.com/docs/12.x/encryption
- **Hashing**: https://laravel.com/docs/12.x/hashing
- **Security Best Practices**: https://laravel.com/docs/12.x/security

### Laravel Deployment
- **Deployment Guide**: https://laravel.com/docs/12.x/deployment
- **Optimization**: https://laravel.com/docs/12.x/deployment#optimization
- **Laravel Envoy**: https://laravel.com/docs/12.x/envoy

## Bootstrap 5 Resources

### Official Documentation
- **Bootstrap 5 Documentation**: https://getbootstrap.com/docs/5.3/
- **Getting Started**: https://getbootstrap.com/docs/5.3/getting-started/introduction/
- **Layout System**: https://getbootstrap.com/docs/5.3/layout/grid/
- **Components**: https://getbootstrap.com/docs/5.3/components/

### Project-Specific Bootstrap Features
- **Grid System**: https://getbootstrap.com/docs/5.3/layout/grid/
- **Responsive Design**: https://getbootstrap.com/docs/5.3/layout/breakpoints/
- **Print Utilities**: https://getbootstrap.com/docs/5.3/utilities/display/#print
- **Cards Component**: https://getbootstrap.com/docs/5.3/components/card/
- **Typography**: https://getbootstrap.com/docs/5.3/content/typography/
- **Tables**: https://getbootstrap.com/docs/5.3/content/tables/

### Bootstrap Customization
- **Sass Variables**: https://getbootstrap.com/docs/5.3/customize/sass/
- **CSS Variables**: https://getbootstrap.com/docs/5.3/customize/css-variables/
- **Color System**: https://getbootstrap.com/docs/5.3/customize/color/

### Print CSS Resources
- **CSS Print Media Queries**: https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_media_queries/Using_media_queries#print
- **Print Stylesheet Best Practices**: https://www.smashingmagazine.com/2011/11/how-to-set-up-a-print-style-sheet/
- **Bootstrap Print Utilities**: https://getbootstrap.com/docs/5.3/utilities/display/#print

## Chatwoot Resources

### Official Documentation
- **Chatwoot Documentation**: https://www.chatwoot.com/docs/
- **Self-Hosted Installation**: https://www.chatwoot.com/docs/self-hosted/deployment/linux-vm
- **Docker Installation**: https://www.chatwoot.com/docs/self-hosted/deployment/docker
- **Configuration**: https://www.chatwoot.com/docs/self-hosted/configuration/

### Chatwoot Integration
- **Widget Installation**: https://www.chatwoot.com/docs/product/channels/live-chat/create-website-channel
- **JavaScript SDK**: https://www.chatwoot.com/docs/product/channels/live-chat/sdk/setup
- **Webhooks**: https://www.chatwoot.com/docs/product/others/webhooks
- **API Documentation**: https://www.chatwoot.com/developers/api/

### Project-Specific Chatwoot Features
- **Custom Attributes**: https://www.chatwoot.com/docs/product/others/custom-attributes
- **Contact Management**: https://www.chatwoot.com/docs/user-guide/contacts
- **Conversation Management**: https://www.chatwoot.com/docs/user-guide/conversations
- **Team Management**: https://www.chatwoot.com/docs/user-guide/teams

### Chatwoot Self-Hosting
- **AWS Deployment Guide**: https://www.chatwoot.com/docs/self-hosted/deployment/aws
- **Environment Variables**: https://www.chatwoot.com/docs/self-hosted/configuration/environment-variables
- **Database Configuration**: https://www.chatwoot.com/docs/self-hosted/configuration/database
- **Redis Configuration**: https://www.chatwoot.com/docs/self-hosted/configuration/redis

## Database & Versioning

### MySQL/Database Resources
- **Laravel Database**: https://laravel.com/docs/12.x/database
- **Query Builder**: https://laravel.com/docs/12.x/queries
- **Schema Builder**: https://laravel.com/docs/12.x/migrations#creating-tables
- **Database Indexing**: https://dev.mysql.com/doc/refman/8.0/en/optimization-indexes.html

### Versioning System Implementation
- **Database Design Patterns**: https://martinfowler.com/articles/evodb.html
- **Temporal Data Patterns**: https://martinfowler.com/eaaDev/TemporalObject.html
- **Laravel Model Versioning**: https://github.com/overtrue/laravel-versionable

### Data Migration Resources
- **Laravel Migrations**: https://laravel.com/docs/12.x/migrations
- **Database Seeding**: https://laravel.com/docs/12.x/seeding
- **Schema Dumping**: https://laravel.com/docs/12.x/migrations#squashing-migrations

## Security Implementation

### Laravel Security
- **Security Documentation**: https://laravel.com/docs/12.x/security
- **CSRF Protection**: https://laravel.com/docs/12.x/csrf
- **Input Validation**: https://laravel.com/docs/12.x/validation
- **Rate Limiting**: https://laravel.com/docs/12.x/rate-limiting

### Web Security Best Practices
- **OWASP Top 10**: https://owasp.org/www-project-top-ten/
- **Content Security Policy**: https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP
- **HTTPS Configuration**: https://letsencrypt.org/docs/
- **Security Headers**: https://securityheaders.com/

### URL Security
- **Signed URLs**: https://laravel.com/docs/12.x/urls#signed-urls
- **URL Expiration**: https://laravel.com/docs/12.x/urls#urls-with-expiration
- **Route Model Binding**: https://laravel.com/docs/12.x/routing#route-model-binding

## AWS Deployment

### AWS Documentation
- **EC2 User Guide**: https://docs.aws.amazon.com/ec2/
- **RDS User Guide**: https://docs.aws.amazon.com/rds/
- **Application Load Balancer**: https://docs.aws.amazon.com/elasticloadbalancing/latest/application/
- **Route 53**: https://docs.aws.amazon.com/route53/
- **CloudWatch**: https://docs.aws.amazon.com/cloudwatch/

### Laravel on AWS
- **Laravel AWS Deployment**: https://laravel.com/docs/12.x/deployment#server-configuration
- **AWS CodeDeploy**: https://docs.aws.amazon.com/codedeploy/
- **AWS Systems Manager**: https://docs.aws.amazon.com/systems-manager/

### SSL/TLS Configuration
- **AWS Certificate Manager**: https://docs.aws.amazon.com/acm/
- **Let's Encrypt**: https://letsencrypt.org/getting-started/
- **SSL Configuration**: https://mozilla.github.io/server-side-tls/ssl-config-generator/

## Development Tools

### DDEV Local Development
- **DDEV Documentation**: https://ddev.readthedocs.io/
- **DDEV Laravel Setup**: https://ddev.readthedocs.io/en/stable/users/quickstart/#laravel
- **DDEV Configuration**: https://ddev.readthedocs.io/en/stable/users/configuration/config/

### Vite Asset Bundling
- **Laravel Vite**: https://laravel.com/docs/12.x/vite
- **Vite Documentation**: https://vitejs.dev/guide/
- **Asset Compilation**: https://laravel.com/docs/12.x/vite#running-vite

### Version Control
- **Git Best Practices**: https://git-scm.com/book/en/v2
- **Laravel Envoy**: https://laravel.com/docs/12.x/envoy
- **Deployment Scripts**: https://laravel.com/docs/12.x/envoy#writing-envoy-tasks

## Testing Resources

### Laravel Testing
- **Testing Documentation**: https://laravel.com/docs/12.x/testing
- **HTTP Tests**: https://laravel.com/docs/12.x/http-tests
- **Database Testing**: https://laravel.com/docs/12.x/database-testing
- **Mocking**: https://laravel.com/docs/12.x/mocking

### Frontend Testing
- **Browser Testing**: https://laravel.com/docs/12.x/dusk
- **JavaScript Testing**: https://vitest.dev/
- **Responsive Testing Tools**: https://responsivedesignchecker.com/

### Security Testing
- **Laravel Security**: https://github.com/enlightn/enlightn
- **OWASP Testing Guide**: https://owasp.org/www-project-web-security-testing-guide/
- **Vulnerability Scanning**: https://github.com/sensiolabs/security-checker

## Project-Specific Examples

### URL Generation for Proposals
```php
// Generate secure proposal URL with expiration
Route::get('/proposal/{token}', [ProposalController::class, 'show'])
    ->name('proposal.show')
    ->middleware('signed');

// In Controller
$url = URL::temporarySignedRoute(
    'proposal.show',
    now()->addHours(48),
    ['token' => $proposal->secure_token]
);
```

### Proposal Versioning Database Schema
```sql
-- Proposals table
CREATE TABLE proposals (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    customer_id BIGINT UNSIGNED NOT NULL,
    base_proposal_id BIGINT UNSIGNED NULL,
    version_number VARCHAR(10) NOT NULL,
    version_status ENUM('draft', 'sent', 'accepted', 'rejected', 'superseded'),
    secure_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customer_id (customer_id),
    INDEX idx_secure_token (secure_token),
    INDEX idx_expires_at (expires_at)
);

-- Proposal versions table
CREATE TABLE proposal_versions (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    proposal_id BIGINT UNSIGNED NOT NULL,
    version_data JSON NOT NULL,
    created_by BIGINT UNSIGNED NOT NULL,
    notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (proposal_id) REFERENCES proposals(id) ON DELETE CASCADE
);
```

### Chatwoot Widget Integration
```html
<!-- In Blade template -->
<script>
window.chatwootSettings = {
    hideMessageBubble: false,
    position: 'right',
    locale: 'en',
    type: 'standard',
    customAttributes: {
        proposalId: '{{ $proposal->id }}',
        proposalVersion: '{{ $proposal->version_number }}',
        customerName: '{{ $proposal->customer_name }}',
        proposalValue: '{{ $proposal->total_value }}'
    }
};

(function(d,t) {
    var BASE_URL="https://your-chatwoot-domain.com";
    var g=d.createElement(t),s=d.getElementsByTagName(t)[0];
    g.src=BASE_URL+"/packs/js/sdk.js";
    g.defer=true;
    g.async=true;
    s.parentNode.insertBefore(g,s);
    g.onload=function(){
        window.chatwootSDK.run({
            websiteToken: 'your-website-token',
            baseUrl: BASE_URL
        })
    }
})(document,"script");
</script>
```

### Print CSS Example
```css
/* Print-specific styles */
@media print {
    /* Hide navigation and non-essential elements */
    .navbar, .chat-widget, .btn, .no-print {
        display: none !important;
    }

    /* Optimize typography for print */
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: #000;
        background: #fff;
    }

    /* Ensure proposal content spans full width */
    .proposal-content {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
    }

    /* Page break controls */
    .page-break {
        page-break-before: always;
    }

    .no-break {
        page-break-inside: avoid;
    }

    /* Optimize tables for print */
    table {
        border-collapse: collapse;
        width: 100%;
    }

    th, td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
    }
}
```

### Bootstrap Responsive Grid for Proposals
```html
<!-- Responsive proposal layout -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12 col-lg-8">
            <!-- Main proposal content -->
            <div class="card proposal-content">
                <div class="card-header">
                    <h2>Holiday Quote - Version {{ $proposal->version_number }}</h2>
                    <small class="text-muted">Generated: {{ $proposal->created_at->format('M d, Y') }}</small>
                </div>
                <div class="card-body">
                    <!-- Proposal details -->
                </div>
            </div>
        </div>
        <div class="col-12 col-lg-4 d-print-none">
            <!-- Sidebar with version history, chat widget -->
            <div class="card">
                <div class="card-header">
                    <h5>Version History</h5>
                </div>
                <div class="card-body">
                    <!-- Version list -->
                </div>
            </div>
        </div>
    </div>
</div>
```

### Laravel Validation for Proposal Access
```php
// Custom validation rule for proposal access
class ProposalAccessRule implements Rule
{
    public function passes($attribute, $value)
    {
        $proposal = Proposal::where('secure_token', $value)
            ->where('expires_at', '>', now())
            ->where('version_status', '!=', 'draft')
            ->first();

        return $proposal !== null;
    }

    public function message()
    {
        return 'The proposal link is invalid or has expired.';
    }
}

// In Controller
public function show(Request $request, $token)
{
    $request->validate([
        'token' => ['required', new ProposalAccessRule()]
    ]);

    $proposal = Proposal::where('secure_token', $token)->firstOrFail();

    // Log access
    ProposalAccess::create([
        'proposal_id' => $proposal->id,
        'ip_address' => $request->ip(),
        'user_agent' => $request->userAgent(),
        'accessed_at' => now()
    ]);

    return view('proposals.show', compact('proposal'));
}
```

## Additional Resources

### Community Resources
- **Laravel Community**: https://laravel.io/
- **Laravel News**: https://laravel-news.com/
- **Laracasts**: https://laracasts.com/
- **Bootstrap Community**: https://github.com/twbs/bootstrap/discussions
- **Chatwoot Community**: https://github.com/chatwoot/chatwoot/discussions

### Tutorials and Guides
- **Laravel From Scratch**: https://laracasts.com/series/laravel-from-scratch
- **Bootstrap 5 Tutorial**: https://www.w3schools.com/bootstrap5/
- **Chatwoot Setup Guide**: https://www.chatwoot.com/docs/self-hosted/deployment/
- **AWS Laravel Deployment**: https://aws.amazon.com/getting-started/hands-on/deploy-php-application/

### Development Best Practices
- **Laravel Best Practices**: https://github.com/alexeymezenin/laravel-best-practices
- **PHP Standards**: https://www.php-fig.org/psr/
- **Database Design**: https://www.vertabelo.com/blog/database-design-best-practices/
- **Security Checklist**: https://github.com/paragonie/awesome-appsec

### Monitoring and Debugging
- **Laravel Telescope**: https://laravel.com/docs/12.x/telescope
- **Laravel Debugbar**: https://github.com/barryvdh/laravel-debugbar
- **AWS CloudWatch**: https://docs.aws.amazon.com/cloudwatch/
- **Application Performance Monitoring**: https://docs.newrelic.com/docs/apm/

### Package Recommendations
- **Laravel Packages**: https://packagist.org/packages/laravel/
- **Spatie Packages**: https://spatie.be/open-source/packages
- **Laravel Collective**: https://laravelcollective.com/
- **Intervention Image**: http://image.intervention.io/ (for image processing if needed)

---

## Quick Reference Commands

### Laravel Artisan Commands
```bash
# Create migration
php artisan make:migration create_proposals_table

# Create model with migration
php artisan make:model Proposal -m

# Create controller
php artisan make:controller ProposalController

# Run migrations
php artisan migrate

# Clear caches
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Generate application key
php artisan key:generate

# Create symbolic link for storage
php artisan storage:link
```

### DDEV Commands
```bash
# Start DDEV
ddev start

# SSH into container
ddev ssh

# Run composer
ddev composer install

# Run artisan commands
ddev exec php artisan migrate

# Import database
ddev import-db --src=database.sql
```

### Git Deployment Commands
```bash
# Basic deployment script
git pull origin main
composer install --no-dev --optimize-autoloader
php artisan migrate --force
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan queue:restart
```

This reference guide provides comprehensive documentation links and project-specific examples that will be invaluable during development of the BV Quote App.
